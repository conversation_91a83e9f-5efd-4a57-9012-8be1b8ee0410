/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "left_robot.h"

#include <chrono>
#include <cmath>
#include <thread>

#include <aubo-base/log.h>
#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

#include "../../core/coffee_order.h"
#include "latte_art_config.h"
#include "robot_base.h"

namespace aubo {

// 配置常量
constexpr const char* LEFT_ROBOT_HOST = "*************";
constexpr int LEFT_ROBOT_PORT = 8899;

class LeftRobot::Impl : public RobotBase {
public:
    Impl(LeftRobot* parent) : RobotBase("LeftRobot", LEFT_ROBOT_HOST, LEFT_ROBOT_PORT), parent_(parent) {}

    ~Impl() {
        LOG_INFO("[LeftRobot] ~Impl");
    }

    bool on_init_specific() override {
        LOG_INFO("[LeftRobot] 执行左臂特定初始化");

        // 加载拉花配置
        if (!latte_art_config_.load_from_directory("../share/config/latte_art")) {
            LOG_WARN("[LeftRobot] 无法加载拉花配置目录，请检查配置文件");
        } else {
            LOG_INFO("[LeftRobot] 拉花配置加载成功");
        }

        LOG_INFO("[LeftRobot] 左臂特定初始化完成");
        return true;
    }

    bool execute_action(const std::string& action_name) {
        LOG_INFO("[LeftRobot] 执行动作: {}", action_name);
        // TODO: 实现通用动作执行逻辑
        return true;
    }

    bool move_to_home() override {
        return execute_action("move_to_home");
    }

    bool move_to_ready() override {
        return execute_action("move_to_ready");
    }

private:
    LatteArtConfig latte_art_config_;
};


LeftRobot::LeftRobot() {
    impl_ = std::make_unique<Impl>(this);
}

LeftRobot::~LeftRobot() = default;

bool LeftRobot::init() {
    return impl_->init();
}

bool LeftRobot::shutdown() {
    return impl_->shutdown();
}

bool LeftRobot::emergency_stop() {
    return impl_->emergency_stop();
}

bool LeftRobot::move_to_home() {
    return impl_->execute_action("move_to_home");
}

bool LeftRobot::move_to_ready() {
    return impl_->execute_action("move_to_ready");
}

bool LeftRobot::move_to_cup_outlet() {
    return impl_->execute_action("move_to_cup_outlet");
}

bool LeftRobot::move_to_coffee_outlet() {
    return impl_->execute_action("move_to_coffee_outlet");
}

bool LeftRobot::move_to_latte_art() {
    return impl_->execute_action("move_to_latte_art");
}

bool LeftRobot::do_latte_art(const std::string& art_type) {
    std::string action_name = "do_latte_art:" + art_type;
    return impl_->execute_action(action_name);
}

bool LeftRobot::deliver_coffee() {
    return impl_->execute_action("deliver_coffee");
}

} // namespace aubo
