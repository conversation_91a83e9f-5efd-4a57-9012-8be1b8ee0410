/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "left_robot.h"

#include <chrono>
#include <cmath>
#include <thread>

#include <aubo-base/log.h>
#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

#include "../../core/coffee_order.h"
#include "latte_art_config.h"
#include "robot_base.h"

namespace aubo {

// 配置常量
constexpr const char* LEFT_ROBOT_HOST = "*************";
constexpr int LEFT_ROBOT_PORT = 8899;

class LeftRobot::Impl : public RobotBase {
public:
    Impl(LeftRobot* parent) : RobotBase("LeftRobot", LEFT_ROBOT_HOST, LEFT_ROBOT_PORT), parent_(parent) {}

    ~Impl() {
        LOG_INFO("[LeftRobot] ~Impl");
    }

    bool on_init_specific() override {
        LOG_INFO("[LeftRobot] 执行左臂特定初始化");

        // 配置运动参数
        configure_movement_parameters();

        // 加载拉花配置
        if (!latte_art_config_.load_from_directory("../share/config/latte_art")) {
            LOG_WARN("[LeftRobot] 无法加载拉花配置目录，请检查配置文件");
        } else {
            LOG_INFO("[LeftRobot] 拉花配置加载成功");
        }

        LOG_INFO("[LeftRobot] 左臂特定初始化完成");
        return true;
    }

    bool move_to_cup_outlet() {
        LOG_INFO("[LeftRobot] 移动到杯子出口位置");
        // TODO: 实现移动到杯子出口位置的逻辑
        return true;
    }

    bool move_to_coffee_outlet() {
        LOG_INFO("[LeftRobot] 移动到咖啡出口位置");
        // TODO: 实现移动到咖啡出口位置的逻辑
        return true;
    }

    bool prepare_for_latte_art() {
        LOG_INFO("[LeftRobot] 准备拉花位置");
        // TODO: 实现移动到拉花位置的逻辑
        return true;
    }

    bool execute_latte_art_sequence(LatteArtType art_type = LatteArtType::NONE) {
        LOG_INFO("[LeftRobot] 执行拉花动作: {}", get_latte_art_name(art_type));
        // TODO: 实现拉花动作的逻辑
        return true;
    }

    bool execute_coffee_delivery_sequence() {
        LOG_INFO("[LeftRobot] 执行咖啡交付序列");
        // TODO: 实现咖啡交付的逻辑
        return true;
    }

    // 重写基类的 move_to_home 方法，提供左臂特定的初始位置
    bool move_to_home() override {
        LOG_INFO("[LeftRobot] 移动到初始位置");
        // TODO: 实现移动到初始位置的逻辑
        return true;
    }

    // 重写基类的 move_to_ready 方法，提供左臂特定的准备位置
    bool move_to_ready() override {
        LOG_INFO("[LeftRobot] 移动到准备位置");
        // TODO: 实现移动到准备位置的逻辑
        return true;
    }

private:
    // 配置运动参数
    void configure_movement_parameters() {
        // 设置关节运动最大加速度
        aubo_robot_namespace::JointVelcAccParam joint_max_acc;
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_max_acc.jointPara[i] = 2.0;
        }
        auto result = robot_service_.robotServiceSetGlobalMoveJointMaxAcc(joint_max_acc);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置关节运动最大加速度失败, 结果 = {}", result);
        }

        // 设置关节运动最大速度
        aubo_robot_namespace::JointVelcAccParam joint_max_velc;
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_max_velc.jointPara[i] = 2.0;
        }
        result = robot_service_.robotServiceSetGlobalMoveJointMaxVelc(joint_max_velc);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置关节运动最大速度失败, 结果 = {}", result);
        }

        // 设置末端执行器运动参数
        result = robot_service_.robotServiceSetGlobalMoveEndMaxLineAcc(0.436332);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置末端执行器线性加速度失败, 结果 = {}", result);
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxAngleAcc(2.0);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置末端执行器角度加速度失败, 结果 = {}", result);
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxLineVelc(0.436332);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置末端执行器线性速度失败, 结果 = {}", result);
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxAngleVelc(2.0);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置末端执行器角度速度失败, 结果 = {}", result);
        }
    }

    LatteArtConfig latte_art_config_;
    LeftRobot* parent_;
};

// LeftRobot 公共接口实现
LeftRobot::LeftRobot() {
    impl_ = std::make_unique<Impl>(this);
}

LeftRobot::~LeftRobot() = default;

bool LeftRobot::init() {
    // 调用Impl的初始化逻辑
    return impl_->init();
}

bool LeftRobot::shutdown() {
    return impl_->shutdown();
}

bool LeftRobot::emergency_stop() {
    return impl_->emergency_stop();
}

std::string LeftRobot::get_status() const {
    return impl_->get_status();
}

bool LeftRobot::is_connected() const {
    return impl_->is_connected();
}

bool LeftRobot::move_to_home() {
    return impl_->move_to_home();
}

bool LeftRobot::move_to_ready() {
    return impl_->move_to_ready();
}

bool LeftRobot::move_to_cup_outlet() {
    return impl_->move_to_cup_outlet();
}

bool LeftRobot::move_to_coffee_outlet() {
    return impl_->move_to_coffee_outlet();
}

bool LeftRobot::move_to_latte_art() {
    return impl_->prepare_for_latte_art();
}

bool LeftRobot::do_latte_art(const std::string& art_type) {
    LatteArtType latte_art_type = LatteArtType::NONE;
    if (art_type == "heart") {
        latte_art_type = LatteArtType::HEART;
    } else if (art_type == "leaf") {
        latte_art_type = LatteArtType::LEAF;
    } else if (art_type == "tulip") {
        latte_art_type = LatteArtType::TULIP;
    } else if (art_type == "swan") {
        latte_art_type = LatteArtType::SWAN;
    }

    return impl_->execute_latte_art_sequence(latte_art_type);
}

bool LeftRobot::deliver_coffee() {
    return impl_->execute_coffee_delivery_sequence();
}

} // namespace aubo
